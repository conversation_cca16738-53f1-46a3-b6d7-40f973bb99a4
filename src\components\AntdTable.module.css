/* 自定义表格样式 */
.custom-table .ant-table {
  border-radius: 16px;
  overflow: hidden;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.custom-table .ant-table-thead > tr > th {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  font-weight: 600;
  color: #374151;
  padding: 16px 12px;
  position: relative;
}

.custom-table .ant-table-thead > tr > th::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
}

.custom-table .ant-table-tbody > tr {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.custom-table .ant-table-tbody > tr:hover {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.custom-table .ant-table-tbody > tr:hover::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #3b82f6, #8b5cf6);
  border-radius: 0 4px 4px 0;
}

.custom-table .ant-table-tbody > tr > td {
  padding: 14px 12px;
  border-bottom: 1px solid #f1f5f9;
  transition: all 0.2s ease;
}

.custom-table .ant-table-row-selected {
  background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%) !important;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.15);
}

.custom-table .ant-table-row-selected:hover {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%) !important;
  transform: translateY(-2px);
}

.custom-table .ant-table-row-selected::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #3b82f6, #1d4ed8);
  border-radius: 0 4px 4px 0;
}

/* 增强表格样式 */
.enhanced-table .ant-table-container {
  border-radius: 12px;
  overflow: hidden;
}

.enhanced-table .ant-table-tbody > tr:nth-child(even) {
  background-color: rgba(248, 250, 252, 0.5);
}

.enhanced-table .ant-table-tbody > tr:nth-child(odd) {
  background-color: rgba(255, 255, 255, 0.8);
}

/* 自定义分页样式 */
.custom-pagination .ant-pagination-item {
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.custom-pagination .ant-pagination-item:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.custom-pagination .ant-pagination-item-active {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border-color: #3b82f6;
  box-shadow: 0 4px 15px rgba(59, 130, 246, 0.3);
  transform: translateY(-1px);
}

.custom-pagination .ant-pagination-item-active a {
  color: white;
  font-weight: 600;
}

.custom-pagination .ant-pagination-prev,
.custom-pagination .ant-pagination-next {
  border-radius: 10px;
  border: 1px solid #e2e8f0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.custom-pagination .ant-pagination-prev:hover,
.custom-pagination .ant-pagination-next:hover {
  border-color: #3b82f6;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

/* 增强分页样式 */
.enhanced-pagination {
  padding: 8px;
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.enhanced-pagination .ant-pagination-item,
.enhanced-pagination .ant-pagination-prev,
.enhanced-pagination .ant-pagination-next {
  margin: 0 2px;
}

.enhanced-pagination .ant-pagination-options {
  margin-left: 16px;
}

.enhanced-pagination .ant-select-selector {
  border-radius: 8px;
  border: 1px solid #e2e8f0;
  transition: all 0.2s ease;
}

.enhanced-pagination .ant-select-selector:hover {
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

/* 自定义Modal样式 */
.custom-modal .ant-modal-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  border-radius: 16px 16px 0 0;
  padding: 24px 28px;
  position: relative;
}

.custom-modal .ant-modal-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
}

.custom-modal .ant-modal-body {
  padding: 28px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.custom-modal .ant-modal-content {
  border-radius: 16px;
  overflow: hidden;
  border: none;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(20px);
}

/* 自定义Drawer样式 */
.custom-drawer .ant-drawer-header {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border-bottom: 2px solid #e2e8f0;
  padding: 24px 28px;
  position: relative;
}

.custom-drawer .ant-drawer-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
}

.custom-drawer .ant-drawer-body {
  padding: 28px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

.custom-drawer .ant-drawer-footer {
  padding: 20px 28px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-top: 1px solid #e2e8f0;
}

/* 表单项样式优化 */
.ant-form-item-label > label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.ant-input,
.ant-select-selector {
  border-radius: 10px;
  border: 1px solid #d1d5db;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.ant-input:hover,
.ant-select-selector:hover {
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), 0 2px 8px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

.ant-input:focus,
.ant-select-focused .ant-select-selector {
  border-color: #3b82f6;
  box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.15), 0 4px 12px rgba(0, 0, 0, 0.1);
  transform: translateY(-1px);
}

/* 按钮样式优化 */
.ant-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  font-weight: 600;
  border-radius: 10px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.ant-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.ant-btn-primary {
  background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  border: none;
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.ant-btn-primary:hover {
  background: linear-gradient(135deg, #1d4ed8 0%, #7c3aed 100%);
  box-shadow: 0 8px 25px rgba(59, 130, 246, 0.4);
}

/* Tag样式优化 */
.ant-tag {
  border-radius: 8px;
  font-weight: 600;
  padding: 4px 12px;
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.ant-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 卡片样式优化 */
.ant-card {
  border-radius: 20px;
  border: none;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(20px);
  background: rgba(255, 255, 255, 0.9);
}

.ant-card-body {
  padding: 0;
}

/* 新增层次感样式 */
.layered-container {
  position: relative;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(248, 250, 252, 0.8) 100%);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.floating-element {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-element:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

/* 响应式优化 */
@media (max-width: 768px) {
  .custom-table .ant-table-thead > tr > th,
  .custom-table .ant-table-tbody > tr > td {
    padding: 8px 6px;
    font-size: 12px;
  }

  .custom-modal .ant-modal-body,
  .custom-drawer .ant-drawer-body {
    padding: 16px;
  }
}

/* 加载动画优化 */
.ant-spin-dot-item {
  background-color: #3b82f6;
}

/* 滚动条样式 */
.ant-table-body::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

.ant-table-body::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.ant-table-body::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
