import React, { useState, useEffect, useCallback } from 'react';
import { Table, Button, Space, Input, Select, Form, Modal, Drawer, Popconfirm, message, Card, Row, Col, Pagination, Tag } from 'antd';
import { SearchOutlined, PlusOutlined, EditOutlined, DeleteOutlined, FilterOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import type { TableColumnsType } from 'antd';
import type { TableRowSelection, FilterDropdownProps } from 'antd/es/table/interface';
import type {
  TaskBasic,
  TaskBasicFormDataAdd,
  TaskBasicFormDataUpdateOrDelete,
  TaskSearchParams,
  TaskSelectionState,
  TaskModalState,
  TaskDrawerState,
  TaskAlert,
  DBConnection,
  AlertSend,
  OtherInfo,
} from '../types/task';
import { TaskService } from '../services/taskService';
import { TASK_STATUS_OPTIONS, WEEKDAY_OPTIONS, FREQUENCY_OPTIONS } from '../types/task';
import ComplexTaskForm from './ComplexTaskForm';
import './AntdTable.module.css';

const { Option } = Select;

/**
 * 任务管理表格组件
 * 包含查询、新增、编辑、删除、分页等完整功能
 */
const AntdTable: React.FC = () => {
  // 表格数据状态
  const [data, setData] = useState<TaskBasic[]>([]);
  const [loading, setLoading] = useState(false);
  const [total, setTotal] = useState(0);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
  });

  // 搜索参数状态
  const [searchParams, setSearchParams] = useState<TaskSearchParams>({});

  // 排序状态
  const [sortedInfo, setSortedInfo] = useState<{
    field?: string;
    order?: 'ascend' | 'descend';
  }>({});

  // 筛选状态
  const [filteredInfo, setFilteredInfo] = useState<Record<string, string[] | null>>({});

  // 表格选择状态 - 支持跨页面选择
  const [selection, setSelection] = useState<TaskSelectionState>({
    selectedRowKeys: [],
    selectedRows: [],
  });

  // 存储所有已选择的行数据（跨页面）
  const [allSelectedRows, setAllSelectedRows] = useState<Map<React.Key, TaskBasic>>(new Map());

  // Modal状态
  const [searchModal, setSearchModal] = useState<TaskModalState>({
    visible: false,
  });

  // 抽屉状态
  const [editDrawer, setEditDrawer] = useState<TaskDrawerState>({
    visible: false,
  });

  const [currentRecord, setCurrentRecord] = useState<TaskBasic | null>(null);

  // 抽屉其他表单数据
  const [taskAlerts, setTaskAlerts] = useState<TaskAlert[]>([]);
  const [dbConnection, setDbConnection] = useState<DBConnection | null>(null);
  const [alertSends, setAlertSends] = useState<AlertSend[]>([]);
  const [otherInfo, setOtherInfo] = useState<OtherInfo | null>(null);

  // 表单实例
  const [searchForm] = Form.useForm();

  // 自定义筛选下拉组件
  const getColumnSearchProps = (dataIndex: string, placeholder: string) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: FilterDropdownProps) => (
      <div style={{ padding: 8 }}>
        <Input
          placeholder={`搜索 ${placeholder}`}
          value={selectedKeys[0]}
          onChange={e => setSelectedKeys(e.target.value ? [e.target.value] : [])}
          onPressEnter={() => confirm()}
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button type='primary' onClick={() => confirm()} icon={<SearchOutlined />} size='small' style={{ width: 90 }}>
            搜索
          </Button>
          <Button
            onClick={() => {
              clearFilters && clearFilters();
              confirm();
            }}
            size='small'
            style={{ width: 90 }}
          >
            重置
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: filtered ? '#1890ff' : undefined }} />,
    onFilter: (value: any, record: TaskBasic) =>
      record[dataIndex as keyof TaskBasic]?.toString().toLowerCase().includes(value.toString().toLowerCase()),
    filteredValue: filteredInfo[dataIndex] || null,
  });

  // 获取选择筛选器配置
  const getColumnSelectProps = (dataIndex: string, options: { text: string; value: string }[]) => ({
    filters: options,
    onFilter: (value: any, record: TaskBasic) => {
      const fieldValue = record[dataIndex as keyof TaskBasic];
      if (dataIndex === 'status') {
        return fieldValue === value;
      }
      return fieldValue?.toString().includes(value.toString());
    },
    filteredValue: filteredInfo[dataIndex] || null,
  });

  // 表格变化处理函数
  const handleTableChange = (pagination: any, filters: any, sorter: any) => {
    console.log('表格变化:', { pagination, filters, sorter });
    setFilteredInfo(filters);
    setSortedInfo(sorter);
  };

  // 加载数据
  const loadData = useCallback(
    async (customParams?: Partial<TaskSearchParams>) => {
      setLoading(true);
      try {
        // 使用传入的自定义参数，如果没有则使用当前状态
        const currentPage = customParams?.current ?? pagination.current;
        const currentPageSize = customParams?.pageSize ?? pagination.pageSize;

        const params = {
          ...searchParams,
          current: currentPage,
          pageSize: currentPageSize,
          ...customParams,
        };

        const response = await TaskService.getTasks(params);
        setData(response.data);
        setTotal(response.total);
      } catch (error) {
        message.error('加载数据失败');
        console.error('加载数据失败:', error);
      } finally {
        setLoading(false);
      }
    },
    [searchParams, pagination]
  );

  // 初始化加载数据
  useEffect(() => {
    loadData();
  }, [loadData]);

  // 搜索表单提交处理
  const handleSearchFormSubmit = useCallback(
    (values: TaskSearchParams) => {
      console.log('搜索表单数据:', values);
      // 暂时打印搜索参数，后续接入后端接口
      const searchData = {
        name: values.name || '',
        group: values.group || '',
        status: values.status || '',
        db_type: values.db_type || '',
        timestamp: new Date().toISOString(),
      };
      console.log('格式化搜索参数:', searchData);

      // 更新搜索参数状态
      setSearchParams(values);
      setPagination({
        current: 1,
        pageSize: 10,
      });
      // 重置排序和筛选状态
      setSortedInfo({});
      setFilteredInfo({});
      // 重置选择状态
      setAllSelectedRows(new Map());
      setSelection({
        selectedRowKeys: [],
        selectedRows: [],
      });
      // 使用搜索参数加载数据
      loadData({
        current: 1,
        pageSize: 10,
        ...values,
      });
    },
    [loadData]
  );

  // 重置搜索
  const handleReset = useCallback(() => {
    setSearchParams({});
    setPagination({
      current: 1,
      pageSize: 10,
    });
    // 重置排序和筛选状态
    setSortedInfo({});
    setFilteredInfo({});
    // 重置时清空选择状态
    setAllSelectedRows(new Map());
    setSelection({
      selectedRowKeys: [],
      selectedRows: [],
    });
    // 重置搜索表单
    searchForm.resetFields();
    // 立即使用重置后的参数加载数据
    loadData({
      current: 1,
      pageSize: 10,
    });
  }, [loadData, searchForm]);

  // 详细查询提交
  const handleAdvancedSearch = useCallback(
    async (values: TaskSearchParams) => {
      setSearchParams(values);
      setPagination(prev => ({
        ...prev,
        current: 1,
      }));
      // 重置排序和筛选状态
      setSortedInfo({});
      setFilteredInfo({});
      setSearchModal({
        visible: false,
      });
      // 详细查询时清空选择状态
      setAllSelectedRows(new Map());
      setSelection({
        selectedRowKeys: [],
        selectedRows: [],
      });
      // 立即使用第一页和新的搜索条件加载数据
      loadData({
        current: 1,
        ...values,
      });
    },
    [loadData]
  );

  // 表格列定义
  const columns: TableColumnsType<TaskBasic> = [
    {
      title: '任务名称',
      dataIndex: 'name',
      key: 'name',
      width: 180,
      ellipsis: true,
      fixed: 'left',
      sorter: (a, b) => a.name.localeCompare(b.name),
      sortOrder: sortedInfo.field === 'name' ? sortedInfo.order : null,
      ...getColumnSearchProps('name', '任务名称'),
    },
    {
      title: '任务分组',
      dataIndex: 'group',
      key: 'group',
      width: 120,
      sorter: (a, b) => a.group.localeCompare(b.group),
      sortOrder: sortedInfo.field === 'group' ? sortedInfo.order : null,
      ...getColumnSelectProps('group', [
        { text: '系统维护', value: '系统维护' },
        { text: '数据备份', value: '数据备份' },
        { text: '监控告警', value: '监控告警' },
        { text: '日志清理', value: '日志清理' },
        { text: '性能优化', value: '性能优化' },
      ]),
    },
    {
      title: '执行时间',
      key: 'time',
      width: 180,
      sorter: (a, b) => a.start_time.localeCompare(b.start_time),
      sortOrder: sortedInfo.field === 'start_time' ? sortedInfo.order : null,
      filteredValue: null,
      render: (_, record) => (
        <span>
          {record.start_time} - {record.end_time}
        </span>
      ),
    },
    {
      title: '星期',
      dataIndex: 'weekday',
      key: 'weekday',
      width: 100,
      sorter: (a, b) => {
        const aWeekday = Array.isArray(a.weekday) ? a.weekday.join(',') : a.weekday;
        const bWeekday = Array.isArray(b.weekday) ? b.weekday.join(',') : b.weekday;
        return aWeekday.localeCompare(bWeekday);
      },
      sortOrder: sortedInfo.field === 'weekday' ? sortedInfo.order : null,
      render: (weekday: string | string[]) => {
        // 如果是数组，转换为逗号分隔的字符串
        if (Array.isArray(weekday)) {
          return weekday.join(',');
        }
        return weekday;
      },
    },
    {
      title: '执行频率',
      dataIndex: 'frequency',
      key: 'frequency',
      width: 100,
      sorter: (a, b) => {
        const aFreq = typeof a.frequency === 'object' ? `${a.frequency.value}${a.frequency.unit}` : a.frequency;
        const bFreq = typeof b.frequency === 'object' ? `${b.frequency.value}${b.frequency.unit}` : b.frequency;
        return aFreq.toString().localeCompare(bFreq.toString());
      },
      sortOrder: sortedInfo.field === 'frequency' ? sortedInfo.order : null,
      filteredValue: null,
      render: (
        frequency:
          | string
          | {
              value: number;
              unit: string;
            }
      ) => {
        // 如果是对象，转换为字符串格式
        if (frequency && typeof frequency === 'object') {
          return `${frequency.value}${frequency.unit}`;
        }
        return frequency;
      },
    },
    {
      title: '状态',
      dataIndex: 'status',
      key: 'status',
      width: 100,
      sorter: (a, b) => a.status.localeCompare(b.status),
      sortOrder: sortedInfo.field === 'status' ? sortedInfo.order : null,
      ...getColumnSelectProps('status', [
        { text: '启用', value: 'enabled' },
        { text: '禁用', value: 'disabled' },
      ]),
      render: (status: string) => <Tag color={status === 'enabled' ? 'success' : 'error'}>{status === 'enabled' ? '启用' : '禁用'}</Tag>,
    },
    {
      title: '重试次数',
      dataIndex: 'retry_num',
      key: 'retry_num',
      width: 120,
      sorter: (a, b) => parseInt(a.retry_num) - parseInt(b.retry_num),
      sortOrder: sortedInfo.field === 'retry_num' ? sortedInfo.order : null,
      ...getColumnSearchProps('retry_num', '重试次数'),
    },
    {
      title: '重试间隔',
      dataIndex: 'retry_frequency',
      key: 'retry_frequency',
      width: 100,
      sorter: (a, b) => {
        const aRetryFreq =
          typeof a.retry_frequency === 'object' ? `${a.retry_frequency.value}${a.retry_frequency.unit}` : a.retry_frequency;
        const bRetryFreq =
          typeof b.retry_frequency === 'object' ? `${b.retry_frequency.value}${b.retry_frequency.unit}` : b.retry_frequency;
        return aRetryFreq.toString().localeCompare(bRetryFreq.toString());
      },
      sortOrder: sortedInfo.field === 'retry_frequency' ? sortedInfo.order : null,
      filteredValue: null,
      render: (
        retry_frequency:
          | string
          | {
              value: number;
              unit: string;
            }
      ) => {
        // 如果是对象，转换为字符串格式
        if (retry_frequency && typeof retry_frequency === 'object') {
          return `${retry_frequency.value}${retry_frequency.unit}`;
        }
        return retry_frequency;
      },
    },
    // {
    //   title: "数据库连接",
    //   dataIndex: "db_connection_id",
    //   key: "db_connection_id",
    //   width: 120,
    //   ellipsis: true,
    // },
    {
      title: '创建时间',
      dataIndex: 'create_time',
      key: 'create_time',
      width: 160,
      sorter: (a, b) => new Date(a.create_time).getTime() - new Date(b.create_time).getTime(),
      sortOrder: sortedInfo.field === 'create_time' ? sortedInfo.order : null,
      ...getColumnSearchProps('create_time', '创建时间'),
    },
    {
      title: '操作',
      key: 'action',
      width: 170,
      fixed: 'right',
      filteredValue: null,
      render: (_, record) => (
        <Space size='small'>
          <Button
            type='text'
            size='small'
            icon={<EditOutlined />}
            onClick={async () => {
              setCurrentRecord(record);
              setEditDrawer({
                visible: true,
                loading: true,
              });

              try {
                // 根据TaskBasic的id查询抽屉其他表单数据
                if (record.alert_task_id && record.alert_task_id.length > 0) {
                  try {
                    const alertsData = await TaskService.getAlertsByIds(record.alert_task_id);
                    setTaskAlerts(alertsData);
                    if (alertsData.length === 0) {
                      console.warn(`未找到ID为${record.alert_task_id}的告警数据`);
                    }
                  } catch (error) {
                    console.error(`获取告警数据失败:`, error);
                  }
                }

                if (record.alert_send_id && record.alert_send_id.length > 0) {
                  try {
                    const alertSendsData = await TaskService.getAlertSendsByIds(record.alert_send_id);
                    setAlertSends(alertSendsData);
                    if (alertSendsData.length === 0) {
                      console.warn(`未找到ID为${record.alert_send_id}的告警发送数据`);
                    }
                  } catch (error) {
                    console.error(`获取告警发送数据失败:`, error);
                  }
                }

                if (record.db_connection_id) {
                  try {
                    const dbConnectionData = await TaskService.getDbConnectionById(record.db_connection_id);
                    setDbConnection(dbConnectionData);
                    if (!dbConnectionData) {
                      console.warn(`未找到ID为${record.db_connection_id}的数据库连接数据`);
                    }
                  } catch (error) {
                    console.error(`获取数据库连接数据失败:`, error);
                  }
                }

                if (record.other_info_id) {
                  try {
                    const otherInfoData = await TaskService.getOtherInfoById(record.other_info_id);
                    setOtherInfo(otherInfoData);
                    if (!otherInfoData) {
                      console.warn(`未找到ID为${record.other_info_id}的其他信息数据`);
                    }
                  } catch (error) {
                    console.error(`获取其他信息数据失败:`, error);
                  }
                }

                setEditDrawer(prev => ({
                  ...prev,
                  loading: false,
                }));
              } catch (error) {
                console.error('加载抽屉表单数据失败:', error);
                message.error('加载抽屉表单数据失败');
                setEditDrawer(prev => ({
                  ...prev,
                  loading: false,
                }));
              }
            }}
            className='text-blue-600 hover:text-blue-700 hover:bg-blue-50 rounded-md px-2 py-1 transition-all duration-200'
          >
            编辑
          </Button>
          <Popconfirm
            title='确认删除'
            description='确定要删除这个任务吗？'
            onConfirm={() => handleDelete(record.id)}
            okText='确定'
            cancelText='取消'
            placement='topRight'
          >
            <Button
              type='text'
              size='small'
              danger
              icon={<DeleteOutlined />}
              className='text-red-600 hover:text-red-700 hover:bg-red-50 rounded-md px-2 py-1 transition-all duration-200'
            >
              删除
            </Button>
          </Popconfirm>
        </Space>
      ),
    },
  ];

  // 更新当前页面的选择状态
  useEffect(() => {
    // 根据全局选择状态更新当前页面的选择
    const currentPageSelectedKeys = data.filter(item => allSelectedRows.has(item.id)).map(item => item.id);

    const currentPageSelectedRows = data.filter(item => allSelectedRows.has(item.id));

    setSelection({
      selectedRowKeys: currentPageSelectedKeys,
      selectedRows: currentPageSelectedRows,
    });
  }, [data, allSelectedRows]);

  // 表格行选择配置 - 支持跨页面选择
  const rowSelection: TableRowSelection<TaskBasic> = {
    selectedRowKeys: selection.selectedRowKeys,
    onChange: (selectedRowKeys: React.Key[], selectedRows: TaskBasic[]) => {
      // 更新当前页面的选择状态
      setSelection({
        selectedRowKeys,
        selectedRows,
      });

      // 更新全局选择状态
      const newAllSelectedRows = new Map(allSelectedRows);

      // 移除当前页面中未选中的项
      data.forEach(item => {
        if (!selectedRowKeys.includes(item.id)) {
          newAllSelectedRows.delete(item.id);
        }
      });

      // 添加当前页面中新选中的项
      selectedRows.forEach(row => {
        newAllSelectedRows.set(row.id, row);
      });

      setAllSelectedRows(newAllSelectedRows);
    },
    onSelectAll: (selected: boolean) => {
      const newAllSelectedRows = new Map(allSelectedRows);

      if (selected) {
        // 全选当前页面
        data.forEach(row => {
          newAllSelectedRows.set(row.id, row);
        });
      } else {
        // 取消选择当前页面
        data.forEach(row => {
          newAllSelectedRows.delete(row.id);
        });
      }

      setAllSelectedRows(newAllSelectedRows);

      // 更新当前页面选择状态
      const currentPageKeys = selected ? data.map(item => item.id) : [];
      const currentPageRows = selected ? data : [];
      setSelection({
        selectedRowKeys: currentPageKeys,
        selectedRows: currentPageRows,
      });
    },
  };

  // 删除单个任务
  const handleDelete = useCallback(
    async (id: number) => {
      try {
        await TaskService.deleteTask(id);
        message.success('删除成功');
        loadData();
      } catch (error) {
        message.error('删除失败');
        console.error('删除失败:', error);
      }
    },
    [loadData]
  );

  // 批量删除任务
  const handleBatchDelete = useCallback(async () => {
    const totalSelected = allSelectedRows.size;
    if (totalSelected === 0) {
      message.warning('请先选择要删除的任务');
      return;
    }

    Modal.confirm({
      title: '确认批量删除',
      content: `确定要删除选中的 ${totalSelected} 个任务吗？`,
      icon: <ExclamationCircleOutlined />,
      okText: '确定',
      cancelText: '取消',
      onOk: async () => {
        try {
          const ids = Array.from(allSelectedRows.keys()).map(key => Number(key));
          await TaskService.batchDeleteTasks(ids);
          message.success('批量删除成功');
          // 清空所有选择状态
          setAllSelectedRows(new Map());
          setSelection({
            selectedRowKeys: [],
            selectedRows: [],
          });
          loadData();
        } catch (error) {
          message.error('批量删除失败');
          console.error('批量删除失败:', error);
        }
      },
    });
  }, [allSelectedRows, loadData]);

  // 编辑任务提交
  const handleEditSubmit = useCallback(
    async (values: TaskBasicFormDataAdd) => {
      if (!currentRecord) return;

      try {
        setEditDrawer(prev => ({
          ...prev,
          loading: true,
        }));

        // 根据是否有ID区分新增和更新
        if (currentRecord.id) {
          // 更新操作 - 使用TaskBasicFormDataUpdateOrDelete类型
          const updateData: TaskBasicFormDataUpdateOrDelete = {
            ...values,
            id: currentRecord.id,
            alert_task_id: taskAlerts.map(alert => `alert_${alert.id}`),
            alert_send_id: alertSends.map(send => `send_${send.id}`),
            db_connection_id: dbConnection ? `db_${dbConnection.id}` : '',
            other_info_id: otherInfo ? `info_${otherInfo.id}` : '',
          };

          await TaskService.updateComplexForm(currentRecord.id, updateData);
        } else {
          // 新增操作 - 使用TaskBasicFormDataAdd类型
          const addData: TaskBasicFormDataAdd = {
            ...values,
            alert_task_id: taskAlerts.map(alert => `alert_${alert.id}`),
            alert_send_id: alertSends.map(send => `send_${send.id}`),
            db_connection_id: dbConnection ? `db_${dbConnection.id}` : '',
            other_info_id: otherInfo ? `info_${otherInfo.id}` : '',
          };

          await TaskService.saveComplexForm(addData);
        }

        message.success(currentRecord.id ? '更新成功' : '新增成功');
        setEditDrawer({
          visible: false,
          loading: false,
        });
        setCurrentRecord(null);
        // 清空抽屉其他表单数据
        setTaskAlerts([]);
        setDbConnection(null);
        setAlertSends([]);
        setOtherInfo(null);
        loadData();
      } catch (error) {
        message.error(currentRecord.id ? '更新失败' : '新增失败');
        console.error(currentRecord.id ? '更新失败:' : '新增失败:', error);
        setEditDrawer(prev => ({
          ...prev,
          loading: false,
        }));
      }
    },
    [currentRecord, loadData, taskAlerts, alertSends, dbConnection, otherInfo]
  );

  // 抽屉关闭处理
  const handleDrawerClose = useCallback(() => {
    setEditDrawer({ visible: false });
    setCurrentRecord(null);
    // 清空抽屉其他表单数据
    setTaskAlerts([]);
    setDbConnection(null);
    setAlertSends([]);
    setOtherInfo(null);
  }, []);

  // console.log(currentRecord);

  return (
    <div className='min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden'>
      {/* 背景装饰元素 */}
      <div className='absolute inset-0 overflow-hidden pointer-events-none'>
        <div className='absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/20 to-purple-400/20 rounded-full blur-3xl'></div>
        <div className='absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-tr from-green-400/20 to-blue-400/20 rounded-full blur-3xl'></div>
        <div className='absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-r from-purple-400/10 to-pink-400/10 rounded-full blur-3xl'></div>
      </div>

      {/* 主要内容区域 */}
      <div className='relative z-10 h-screen flex flex-col p-6'>
        <div className='flex-1 overflow-hidden max-w-7xl mx-auto w-full'>
          {/* 页面标题区域 */}
          {/* <div className='mb-6'>
            <div className='bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl border border-white/20 p-6'>
              <div className='flex items-center justify-between'>
                <div className='flex items-center space-x-4'>
                  <div className='w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-600 rounded-xl flex items-center justify-center shadow-lg'>
                    <SearchOutlined className='text-white text-xl' />
                  </div>
                  <div>
                    <h1 className='text-2xl font-bold bg-gradient-to-r from-gray-800 to-gray-600 bg-clip-text text-transparent'>
                      任务管理中心
                    </h1>
                    <p className='text-gray-500 mt-1'>智能化任务调度与监控平台</p>
                  </div>
                </div>
                <div className='flex items-center space-x-2'>
                  <div className='w-3 h-3 bg-green-400 rounded-full animate-pulse'></div>
                  <span className='text-sm text-gray-600 font-medium'>系统运行正常</span>
                </div>
              </div>
            </div>
          </div> */}

          {/* 主内容卡片 */}
          <Card className='h-full shadow-2xl border-0 rounded-2xl overflow-hidden bg-white/90 backdrop-blur-sm'>
            <div className='h-full flex flex-col'>
              {/* 查询区域 - 增强层次感 */}
              <div
                className='flex-shrink-0 relative'
                style={{
                  height: '160px',
                }}
              >
                {/* 查询区域背景装饰 */}
                <div className='absolute inset-0 bg-gradient-to-r from-blue-50/50 to-indigo-50/50 rounded-t-2xl'></div>
                <div className='absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500'></div>

                {/* 快速搜索区域 */}
                <div className='relative z-10 p-6'>
                  <div className='mb-4'>
                    <h2 className='text-lg font-semibold text-gray-800 flex items-center'>
                      <FilterOutlined className='mr-2 text-blue-500' />
                      快速筛选
                    </h2>
                    <div className='w-16 h-1 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mt-1'></div>
                  </div>

                  <Form form={searchForm} onFinish={handleSearchFormSubmit}>
                    <Row gutter={[20, 16]}>
                      <Col xs={24} sm={12} md={6}>
                        <div className='space-y-2'>
                          <label className='text-sm font-semibold text-gray-700 flex items-center'>
                            <div className='w-2 h-2 bg-blue-500 rounded-full mr-2'></div>
                            任务名称
                          </label>
                          <Form.Item name='name' className='mb-0'>
                            <Input
                              placeholder='请输入任务名称'
                              prefix={<SearchOutlined className='text-gray-400' />}
                              allowClear
                              className='rounded-lg shadow-sm border-gray-200 hover:border-blue-400 focus:border-blue-500 transition-all duration-200'
                            />
                          </Form.Item>
                        </div>
                      </Col>

                      <Col xs={24} sm={12} md={6}>
                        <div className='space-y-2'>
                          <label className='text-sm font-semibold text-gray-700 flex items-center'>
                            <div className='w-2 h-2 bg-green-500 rounded-full mr-2'></div>
                            任务分组
                          </label>
                          <Form.Item name='group' className='mb-0'>
                            <Select placeholder='请选择任务分组' allowClear className='w-full rounded-lg shadow-sm'>
                              <Option value='系统维护'>系统维护</Option>
                              <Option value='数据备份'>数据备份</Option>
                              <Option value='监控告警'>监控告警</Option>
                              <Option value='日志清理'>日志清理</Option>
                              <Option value='性能优化'>性能优化</Option>
                            </Select>
                          </Form.Item>
                        </div>
                      </Col>
                      <Col xs={24} sm={12} md={6}>
                        <div className='space-y-2'>
                          <label className='text-sm font-semibold text-gray-700 flex items-center'>
                            <div className='w-2 h-2 bg-purple-500 rounded-full mr-2'></div>
                            任务状态
                          </label>
                          <Form.Item name='status' className='mb-0'>
                            <Select placeholder='请选择任务状态' allowClear className='w-full rounded-lg shadow-sm'>
                              <Option value='enabled'>启用</Option>
                              <Option value='disabled'>禁用</Option>
                            </Select>
                          </Form.Item>
                        </div>
                      </Col>
                      <Col xs={24} sm={12} md={6}>
                        <div className='space-y-2'>
                          <label className='text-sm font-semibold text-gray-700 flex items-center'>
                            <div className='w-2 h-2 bg-orange-500 rounded-full mr-2'></div>
                            数据库类型
                          </label>
                          <Form.Item name='db_type' className='mb-0'>
                            <Select placeholder='请选择数据库类型' allowClear className='w-full rounded-lg shadow-sm'>
                              <Option value='mysql'>MySQL</Option>
                              <Option value='oracle'>Oracle</Option>
                              <Option value='postgresql'>PostgreSQL</Option>
                              <Option value='sqlserver'>SQL Server</Option>
                            </Select>
                          </Form.Item>
                        </div>
                      </Col>
                    </Row>

                    {/* 操作按钮区域 */}
                    <div className='mt-6 pt-4 border-t border-gray-100'>
                      <Row gutter={[16, 16]}>
                        <Col xs={24} sm={12} md={6}>
                          <Button
                            type='primary'
                            icon={<PlusOutlined />}
                            onClick={() => {
                              // 清空当前记录和其他表单数据
                              setCurrentRecord(null);
                              setTaskAlerts([]);
                              setDbConnection(null);
                              setAlertSends([]);
                              setOtherInfo(null);
                              setEditDrawer({
                                visible: true,
                              });
                            }}
                            className='w-full h-10 rounded-lg shadow-lg bg-gradient-to-r from-green-500 to-emerald-600 hover:from-green-600 hover:to-emerald-700 border-0 font-semibold transform hover:scale-105 transition-all duration-200'
                          >
                            新增任务
                          </Button>
                        </Col>
                        <Col xs={24} sm={12} md={18}>
                          <div className='flex justify-end gap-3'>
                            <Button
                              type='primary'
                              htmlType='submit'
                              icon={<SearchOutlined />}
                              className='h-10 px-6 rounded-lg shadow-lg bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 border-0 font-semibold transform hover:scale-105 transition-all duration-200'
                            >
                              搜索
                            </Button>
                            <Button
                              onClick={handleReset}
                              className='h-10 px-6 rounded-lg shadow-md border-gray-200 hover:border-gray-300 font-medium transform hover:scale-105 transition-all duration-200'
                            >
                              重置
                            </Button>
                            <Button
                              icon={<FilterOutlined />}
                              onClick={() =>
                                setSearchModal({
                                  visible: true,
                                })
                              }
                              className='h-10 px-6 rounded-lg shadow-md border-gray-200 hover:border-blue-300 hover:text-blue-600 font-medium transform hover:scale-105 transition-all duration-200'
                            >
                              详细查询
                            </Button>
                          </div>
                        </Col>
                      </Row>
                    </div>
                  </Form>
                </div>
              </div>

              {/* 中间内容区域 - 包含批量操作和表格 */}
              <div className='flex-1 flex flex-col overflow-hidden'>
                {/* 批量操作栏 - 当有选中项时显示 */}
                {allSelectedRows.size > 0 && (
                  <div className='relative mb-4 flex-shrink-0'>
                    {/* 背景装饰 */}
                    <div className='absolute inset-0 bg-gradient-to-r from-blue-500/10 via-purple-500/10 to-pink-500/10 rounded-xl'></div>
                    <div className='absolute inset-0 bg-white/60 backdrop-blur-sm rounded-xl border border-blue-200/50'></div>

                    {/* 内容 */}
                    <div className='relative z-10 p-4'>
                      <div className='flex items-center justify-between'>
                        <div className='flex items-center space-x-4'>
                          <div className='flex items-center space-x-2'>
                            <div className='w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse'></div>
                            <div
                              className='w-2 h-2 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse'
                              style={{ animationDelay: '0.5s' }}
                            ></div>
                          </div>
                          <div>
                            <span className='text-blue-700 font-semibold text-lg'>已选择 {allSelectedRows.size} 项</span>
                            <p className='text-blue-600/70 text-sm mt-0.5'>支持跨页面选择操作</p>
                          </div>
                        </div>
                        <Space size='middle'>
                          <Button
                            danger
                            onClick={handleBatchDelete}
                            className='h-9 px-4 rounded-lg shadow-md bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 border-0 text-white font-medium transform hover:scale-105 transition-all duration-200'
                          >
                            批量删除
                          </Button>
                          <Button
                            onClick={() => {
                              // 清空所有选择状态
                              setAllSelectedRows(new Map());
                              setSelection({
                                selectedRowKeys: [],
                                selectedRows: [],
                              });
                            }}
                            className='h-9 px-4 rounded-lg shadow-md border-gray-200 hover:border-gray-300 font-medium transform hover:scale-105 transition-all duration-200'
                          >
                            取消全选
                          </Button>
                        </Space>
                      </div>
                    </div>
                  </div>
                )}

                {/* 表格区域 - 增强层次感 */}
                <div className='relative overflow-hidden rounded-xl'>
                  {/* 表格背景装饰 */}
                  <div className='absolute inset-0 bg-gradient-to-br from-white via-gray-50/50 to-blue-50/30'></div>
                  <div className='absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-blue-500 via-purple-500 to-pink-500'></div>

                  {/* 表格容器 */}
                  <div
                    className='relative z-10 overflow-hidden'
                    style={{
                      height:
                        allSelectedRows.size > 0
                          ? 'calc(100vh - 460px)' // 有批量操作栏时的高度
                          : 'calc(100vh - 400px)', // 没有批量操作栏时的高度
                    }}
                  >
                    <Table
                      columns={columns}
                      dataSource={data}
                      rowKey='id'
                      loading={loading}
                      pagination={false}
                      rowSelection={rowSelection}
                      onChange={handleTableChange}
                      scroll={{
                        x: 1200,
                        y:
                          allSelectedRows.size > 0
                            ? 'calc(100vh - 460px - 55px)' // 减去表格头部高度
                            : 'calc(100vh - 400px - 55px)', // 减去表格头部高度
                      }}
                      size='middle'
                      className='custom-table enhanced-table'
                    />
                  </div>
                </div>
              </div>

              {/* 分页区域 - 增强层次感 */}
              <div className='flex-shrink-0 relative mt-4'>
                {/* 分页背景装饰 */}
                <div className='absolute inset-0 bg-gradient-to-r from-gray-50/80 via-white to-gray-50/80 rounded-xl'></div>
                <div className='absolute inset-0 border border-gray-100 rounded-xl'></div>

                {/* 分页内容 */}
                <div className='relative z-10 p-4'>
                  <div className='flex justify-between items-center'>
                    <div className='flex items-center space-x-3'>
                      <div className='flex items-center space-x-2'>
                        <div className='w-3 h-3 bg-gradient-to-r from-green-400 to-emerald-500 rounded-full animate-pulse'></div>
                        <div
                          className='w-2 h-2 bg-gradient-to-r from-emerald-500 to-teal-500 rounded-full animate-pulse'
                          style={{ animationDelay: '0.3s' }}
                        ></div>
                      </div>
                      <div>
                        <span className='text-gray-700 font-semibold'>共 {total} 条数据</span>
                        <p className='text-gray-500 text-sm mt-0.5'>实时数据统计</p>
                      </div>
                    </div>
                    <Pagination
                      current={pagination.current}
                      pageSize={pagination.pageSize}
                      total={total}
                      showSizeChanger
                      showQuickJumper
                      showTotal={(total, range) => `第 ${range[0]}-${range[1]} 条/共 ${total} 条`}
                      className='custom-pagination enhanced-pagination'
                      onChange={(page, pageSize) => {
                        const newPageSize = pageSize || 10;
                        const newPagination = {
                          current: page,
                          pageSize: newPageSize,
                        };

                        // 先更新分页状态
                        setPagination(newPagination);

                        // 立即使用新的分页参数加载数据
                        loadData({
                          current: page,
                          pageSize: newPageSize,
                        });
                      }}
                    />
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </div>
      </div>

      {/* 详细查询Modal */}
      <Modal
        title={
          <div className='flex items-center space-x-2'>
            <FilterOutlined className='text-blue-600' />
            <span className='text-lg font-semibold'>详细查询</span>
          </div>
        }
        open={searchModal.visible}
        onCancel={() =>
          setSearchModal({
            visible: false,
          })
        }
        footer={null}
        width={800}
        className='custom-modal'
      >
        <Form form={searchForm} layout='vertical' onFinish={handleAdvancedSearch} initialValues={searchParams}>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='任务名称' name='name'>
                <Input placeholder='请输入任务名称' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='任务分组' name='group'>
                <Select placeholder='请选择任务分组' allowClear>
                  <Option value='系统维护'>系统维护</Option>
                  <Option value='数据备份'>数据备份</Option>
                  <Option value='监控告警'>监控告警</Option>
                  <Option value='日志清理'>日志清理</Option>
                  <Option value='性能优化'>性能优化</Option>
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='任务状态' name='status'>
                <Select placeholder='请选择任务状态' allowClear>
                  {TASK_STATUS_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='执行频率' name='frequency'>
                <Select placeholder='请选择执行频率' allowClear>
                  {FREQUENCY_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='星期' name='weekday'>
                <Select placeholder='请选择星期' allowClear>
                  {WEEKDAY_OPTIONS.map(option => (
                    <Option key={option.value} value={option.value}>
                      {option.label}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='告警接收人' name='alert_receiver'>
                <Input placeholder='请输入告警接收人' />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={16}>
            <Col span={12}>
              <Form.Item label='开始时间' name='start_time'>
                <Input placeholder='HH:mm:ss' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='结束时间' name='end_time'>
                <Input placeholder='HH:mm:ss' />
              </Form.Item>
            </Col>
          </Row>
          <div className='flex justify-end space-x-3 pt-4 border-t border-gray-200'>
            <Button
              onClick={() => {
                searchForm.resetFields();
                setSearchParams({});
                setSearchModal({
                  visible: false,
                });
              }}
              className='rounded-md px-6'
            >
              重置
            </Button>
            <Button
              type='primary'
              htmlType='submit'
              className='bg-blue-600 hover:bg-blue-700 border-blue-600 hover:border-blue-700 rounded-md px-6'
            >
              查询
            </Button>
          </div>
        </Form>
      </Modal>

      {/* 编辑抽屉 */}
      <Drawer
        title={
          <div className='flex items-center space-x-2'>
            <EditOutlined className='text-blue-600' />
            <span className='text-lg font-semibold'>{currentRecord ? '编辑任务' : '新增任务'}</span>
          </div>
        }
        width='90%'
        open={editDrawer.visible}
        onClose={handleDrawerClose}
        maskClosable={false}
        className='custom-drawer'
        footer={null}
      >
        <ComplexTaskForm
          initialData={currentRecord || undefined}
          onSubmit={handleEditSubmit}
          onCancel={handleDrawerClose}
          onReset={() => {
            // 重置操作的回调
            searchForm.resetFields();
            message.info('表单已重置');
          }}
          loading={editDrawer.loading}
          isEdit={!!currentRecord}
        />
      </Drawer>
    </div>
  );
};

export default AntdTable;
